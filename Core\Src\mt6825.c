#include "mt6825.h"
#include "main.h"
#include "spi.h"

#define arraysize         3

#define MT6816
//#define MT6825
extern uint16_t feedback_resolution;
uint16_t spi0_send_array[arraysize] = {0x8300, 0x8400, 0x8500 };
uint16_t spi0_receive_array[arraysize]; 

uint32_t MT_angle=0;

void Delay( uint16_t i )
{
   while( i-- );
}

uint16_t SPIx_ReadWriteByte(uint16_t byte)
{
	uint16_t retry = 0;
	while( (SPI1->SR&1<<1) == 0 )
	{
		if( ++retry > 200 )
			return 0;
	}
	SPI1->DR = byte;     
	
	retry = 0;
	while( (SPI1->SR&1<<0) == 0 ) 
	{
		if( ++retry > 200 )
			return 0;
	}
	return SPI1->DR;          
}



uint32_t ReadValue(uint32_t u32RegValue)
{
	uint32_t u32Data;

	HAL_GPIO_WritePin(SPI1_CS_GPIO_Port,SPI1_CS_Pin,GPIO_PIN_RESET);

	spi0_receive_array[0] = SPIx_ReadWriteByte(spi0_send_array[0]);
	
	HAL_GPIO_WritePin(SPI1_CS_GPIO_Port,SPI1_CS_Pin,GPIO_PIN_SET);
	HAL_GPIO_WritePin(SPI1_CS_GPIO_Port,SPI1_CS_Pin,GPIO_PIN_RESET);
	
	spi0_receive_array[1] = SPIx_ReadWriteByte(spi0_send_array[1]);
	
	#ifdef MT6825
	spi0_receive_array[2] = SPIx_ReadWriteByte(spi0_send_array[2]);
	#endif
	
	HAL_GPIO_WritePin(SPI1_CS_GPIO_Port,SPI1_CS_Pin,GPIO_PIN_SET);	
	
	#ifdef MT6825
	u32Data=(spi0_receive_array[0]*1024)+((spi0_receive_array[1]&0xfc)*4)+((spi0_receive_array[2]&0xf0)>>4); //for MT6825
	#endif
	
	#ifdef MT6816
	u32Data=(spi0_receive_array[0]*64)+((spi0_receive_array[1]&0xfc)>>2); // for MT6816
	#endif
	
	return(u32Data);
}

/* 
 * 函数名: encoder_to_angle_degree_x100
 * 功能描述: 将编码器值转换为角度值（度×100，精度0.01度）
 * 参数: encoder_pos - 编码器位置值
 * 返回值: 角度值（度×100），范围0-35999（对应0.00-359.99度）
 * 说明: 使用模运算确保角度在0-360度范围内
 */
int32_t encoder_to_angle_degree_x100(int32_t encoder_pos)
{
    // 计算角度：angle = (encoder_pos * 36000) / feedback_resolution
    // 36000 = 360度 × 100（精度0.01度）
    int64_t temp = (int64_t)encoder_pos * 36000;
    int32_t angle_x100 = temp / feedback_resolution;
    
    // 确保角度在0-35999范围内（0.00-359.99度）
    angle_x100 = angle_x100 % 36000;
    if (angle_x100 < 0) {
        angle_x100 += 36000;
    }
    
    return angle_x100;
}


int16_t encoder_to_angle_degree(int32_t encoder_pos)
{
    // 计算角度：angle = (encoder_pos * 3600000) / feedback_resolution
    // 3600000 = 360度 × 10000（精度0.0001度）
    int64_t temp = (int64_t)encoder_pos * 360;
    int32_t angle = temp / feedback_resolution;
    
    // 确保角度在0-3599999范围内（0.0000-359.9999度）
    angle = angle % 360;
    if (angle < 0) {
        angle += 360;
    }
    
    return angle;
}