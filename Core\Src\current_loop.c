#include <stm32f1xx_hal.h> //Sets up the correct chip specifc defines required by arm_math
//#define ARM_MATH_CM4
//#include <arm_math.h>

#include <mcpwm.h>

#include <stdlib.h>
#include <math.h>
//#include <cmsis_os.h>
#include <main.h>
#include <adc.h>
#include <tim.h>
#include <spi.h>
#include <utils.h>
#include "delay.h"

/* 私有常量数据 */
static const int one_by_sqrt3 = 577; // 1/√3的近似值，用于Clarke变换
static const int sqrt3_by_2 = 866;   // √3/2的近似值，用于Clarke变换

int vfactor = 1;                     // 电压因子，用于调整调制信号的幅值
int Ialpha, Ibeta, Ierr_d, Ierr_q, Vd, Vd_filter, Vq, Vq_filter, V_current_control_integral_d=0, V_current_control_integral_q=0, vfactor, mod_d, mod_q, mod_scalefactor, mod_alpha, mod_beta, mod_alpha_filter, mod_beta_filter;
int Vq_out_limit=650, Vd_out_limit=700, kci_sum_limit=100; // 输出限幅和积分饱和限制
float kcp=20, kci=30;                // PI控制器的比例增益和积分增益
short current_in_lpf_a=1000, current_out_lpf_a=1000; // 低通滤波器系数
int check_current_overshot_p=0, check_current_overshot_n=0; // 检测电流过冲的变量
int Driver_IIt_Real=0, Driver_IIt_Current, Driver_IIt_Real_DC=0, Driver_IIt_Current_DC; // 驱动电流相关变量
short Driver_IIt_Filter, Driver_IIt_Filter_DC; // 驱动电流滤波器参数

int currentU_show = 0,currentV_show = 0,currentW_show = 0;
uint16_t dutyA_show = 0,dutyB_show = 0,dutyC_show = 0;
/**
 * @brief   电流环控制函数
 * @details 实现电机的电流环控制，包括ADC值转换、Clarke和Park变换、PI控制、电压限幅、低通滤波、逆Park和Clarke变换以及空间矢量调制等步骤。
 * @param   motors    电机结构体指针
 * @param   Id_des    目标直轴电流
 * @param   Iq_des    目标交轴电流
 */
void Current_loop(Motor_t* motors, int Id_des, int Iq_des) 
{
    // 反转目标电流方向，根据相位方向进行调整
    Id_des = -Id_des * phase_dir;
    Iq_des = -Iq_des * phase_dir;

    // 将ADC值转换为相电流，减去偏移量以校准零点
    motors->PhaseU_current = phase_current_from_adcval(ADCValue[0] - ADC_Offset[0]);
    motors->PhaseV_current = phase_current_from_adcval(ADCValue[1] - ADC_Offset[1]);
    motors->PhaseW_current = -motors->PhaseU_current - motors->PhaseV_current;

    // currentU_show = motors->PhaseU_current;
    // currentV_show = motors->PhaseV_current;
    // currentW_show = motors->PhaseW_current;

    // Clarke变换：将三相电流转换为两相静止坐标系下的电流，Ibeta = (1/√3) * (Iv - Iw)
    Ialpha = motors->PhaseU_current;
    Ibeta = (1000 * (motors->PhaseV_current - motors->PhaseW_current)) / 1732;

    // Park变换：将两相静止坐标系下的电流转换为两相同步旋转坐标系下的电流
    int c = arm_cos_f32(motors->phase); // 计算余弦值
    int s = arm_sin_f32(motors->phase); // 计算正弦值
    Id = (c * Ialpha + s * Ibeta) / 16384; // 直轴电流计算
    Iq = (c * Ibeta - s * Ialpha) / 16384; // 交轴电流计算

    // 计算实际的直轴和交轴电流，考虑相位方向
    Iq_real = -Iq * phase_dir;       
    Id_real = -Id * phase_dir;
 
    if (motor_on)
    {
        // 计算电流误差，为目标电流与实际电流的差值
        Ierr_d = Id_des - Id;
        // Ierr_q = Iq_des - Iq;
       
        // 应用PI控制，计算直轴和交轴电压
        Vd = (V_current_control_integral_d / 10 + (Ierr_d * kcp)) / 1000;
        Vq = (V_current_control_integral_q / 10 + (Ierr_q * kcp)) / 1000;

        // 低通滤波，平滑电压信号
        Vq_filter = Low_pass_filter_1(current_out_lpf_a, Vq, Vq_filter);
        Vd_filter = Low_pass_filter_1(current_out_lpf_a, Vd, Vd_filter);

        // 计算调制信号，基于电压因子调整
        mod_d = vfactor * Vd_filter;
        mod_q = vfactor * Vq_filter;

        // 限制调制信号范围，防止饱和
        if (mod_q > Vq_out_limit)
        {
            mod_q = Vq_out_limit;
        } 
        else if (mod_q < -Vq_out_limit)
        {
            mod_q = -Vq_out_limit;
        }
        else 
        {
            // 更新积分项，并限制积分饱和
            V_current_control_integral_q += Ierr_q * kci;
            if (V_current_control_integral_q > kci_sum_limit)
            {
                V_current_control_integral_q = kci_sum_limit;
            }
            if (V_current_control_integral_q < -kci_sum_limit)
            {
                V_current_control_integral_q = -kci_sum_limit;
            }
        }

        if (mod_d > Vd_out_limit)
        {
            mod_d = Vd_out_limit;
        } 
        else if (mod_d < -Vd_out_limit)
        {
            mod_d = -Vd_out_limit;
        }
        else 
        {
            // 更新积分项，并限制积分饱和
            V_current_control_integral_d += Ierr_d * kci;
            if (V_current_control_integral_d > kci_sum_limit)
            {
                V_current_control_integral_d = kci_sum_limit;
            }
            if (V_current_control_integral_d < -kci_sum_limit)
            {
                V_current_control_integral_d = -kci_sum_limit;
            }
        }
        
        // 逆Park变换：将两相同步旋转坐标系下的电压转换为两相静止坐标系下的电压
        mod_alpha = (c * mod_d - s * mod_q) / 16384;
        mod_beta  = (c * mod_q + s * mod_d) / 16384;

        // 应用空间矢量调制（SVM），计算三相电压
        queue_modulation_timings(motors, mod_alpha, mod_beta);

        // 设置PWM占空比，驱动电机
        motors->motor_timer->Instance->CCR1 = motors->PWM1_Duty;
        motors->motor_timer->Instance->CCR2 = motors->PWM2_Duty;
        motors->motor_timer->Instance->CCR3 = motors->PWM3_Duty;
 
        // 检查电流过冲，记录最大偏差
        if (Iq_des > 0)
        {
            if (Ierr_q < check_current_overshot_p)
                check_current_overshot_p = Ierr_q;
        }
        if (Iq_des < 0)
        {
            if (Ierr_q > check_current_overshot_n)
                check_current_overshot_n = Ierr_q;
        }
    }
}

// Y=A*X+(1-A)*Y0 -> Y=Y0+((X-Y0)*A)/1000; 0<A<1000
int IIt_Remaider=0, IIt_temp=0;
int IIt_filter(int A, int X, int Y)
{
    if (X < 0) X = -X; // 确保输入为正值
    IIt_temp = (X - Y) * A + IIt_Remaider; // 计算滤波后的中间值
    IIt_Remaider = IIt_temp % 60000;       // 保留余数
    return Y + IIt_temp / 60000;           // 返回滤波后的结果
}

int IIt_DC_Remaider=0, IIt_DC_temp=0;
int IIt_DC_filter(int A, int X, int Y)
{
    if (X < 0) X = -X; // 确保输入为正值
    IIt_DC_temp = (X - Y) * A + IIt_DC_Remaider; // 计算滤波后的中间值
    IIt_DC_Remaider = IIt_DC_temp % 60000;       // 保留余数
    return Y + IIt_DC_temp / 60000;              // 返回滤波后的结果
}

/**
 * @brief   初始化电流环状态
 * @details 将电流环的相关变量重置为初始值
 */
void Init_current_loop_state(void)
{
    V_current_control_integral_q = 0; // 重置交轴积分项
    V_current_control_integral_d = 0; // 重置直轴积分项
    mod_q = 0;                        // 重置交轴调制信号
    mod_d = 0;                        // 重置直轴调制信号
    Iq_demand = 0;                    // 重置目标交轴电流需求
}