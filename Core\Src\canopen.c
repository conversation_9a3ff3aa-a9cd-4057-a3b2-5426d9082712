#include "can.h"
#include "mcpwm.h"
#include "vofa_function.h"
#include "Dic.h"
#include "canopen.h"
#include "string.h"
CAN_TxHeaderTypeDef TxMessage1; // CAN发送消息结构体

// 2个3位标识符FIFO
#define CAN1FIFO CAN_RX_FIFO0 // CAN1接收FIFO
#define CAN2FIFO CAN_RX_FIFO1 // CAN2接收FIFO

CAN_TxHeaderTypeDef TxMeg; // CAN发送消息头定义
CAN_RxHeaderTypeDef RxMeg; // CAN接收消息头定义

uint8_t CAN_Rx_Data[8]; // CAN接收数据缓冲区
uint8_t CAN_Tx_Data[8]; // CAN发送数据缓冲区

uint8_t TPDO1_Data[8];		// 发送过程数据对象1缓冲区
uint8_t TPDO2_Data[8];		// 发送过程数据对象2缓冲区（电压温度电流）
uint8_t RPDO1_Data[8];		// 接收过程数据对象1缓冲区

uint16_t CAN_Baudrate = 7;	// CAN波特率设置，默认为5（500kbit/s）
uint8_t Error_register = 0; // 错误寄存器

uint16_t TPDO_Period = 50, tim_count = 0; // TPDO发送周期，1表示0.1ms

void CAN_User_Init(CAN_HandleTypeDef *hcan) // CAN用户初始化函数
{
	CAN_FilterTypeDef sFilterConfig; // CAN过滤器配置结构体
	HAL_StatusTypeDef HAL_Status;	 // HAL状态变量

	TxMeg.IDE = CAN_ID_STD;	  // 使用标准帧ID
	TxMeg.RTR = CAN_RTR_DATA; // 数据帧

	switch (CAN_Baudrate) // 根据波特率设置预分频值
	{
	case 0:
		hcan->Init.Prescaler = 100; // 20kbit/s
		break;
	case 1:
		hcan->Init.Prescaler = 40; // 50kbit/s
		break;
	case 2:
		hcan->Init.Prescaler = 20; // 100kbit/s
		break;
	case 3:
		hcan->Init.Prescaler = 16; // 125kbit/s
		break;
	case 4:
		hcan->Init.Prescaler = 8; // 250kbit/s
		break;
	case 5:
		hcan->Init.Prescaler = 4; // 500kbit/s
		break;
	case 7:
		hcan->Init.Prescaler = 2; // 1000kbit/s
		break;
	default:
		hcan->Init.Prescaler = 8; // 默认250kbit/s
		break;
	}

	if (HAL_CAN_Init(hcan) != HAL_OK) // 初始化CAN
	{
		Error_Handler(); // 如果初始化失败，调用错误处理函数
	}

	sFilterConfig.FilterBank = 0;					   // 使用过滤器0
	sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;  // 设置为ID掩码模式
	sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT; // 32位过滤器

	sFilterConfig.FilterIdHigh = 0X0000; // 过滤器ID高16位
	sFilterConfig.FilterIdLow = 0X0000;	 // 过滤器ID低16位

	sFilterConfig.FilterMaskIdHigh = 0X0000;		   // 过滤器掩码高16位（0表示接收所有）
	sFilterConfig.FilterMaskIdLow = 0X0000;			   // 过滤器掩码低16位
	sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0; // 过滤器关联到FIFO0

	sFilterConfig.FilterActivation = ENABLE; // 激活过滤器
	sFilterConfig.SlaveStartFilterBank = 0;	 // 从模式起始过滤器编号

	HAL_Status = HAL_CAN_ConfigFilter(hcan, &sFilterConfig); // 配置CAN过滤器
	HAL_Status = HAL_CAN_Start(hcan);						 // 启动CAN

	HAL_Status = HAL_CAN_ActivateNotification(hcan, CAN_IT_RX_FIFO0_MSG_PENDING); // 激活FIFO0接收中断
}

void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan) // CAN接收中断回调函数
{
	HAL_StatusTypeDef HAL_RetVal;

	HAL_RetVal = HAL_CAN_GetRxMessage(hcan, CAN1FIFO, &RxMeg, CAN_Rx_Data); // 获取接收到的消息
	if (HAL_RetVal == HAL_OK)												// 如果接收成功
	{
		Process_RPDO(&RxMeg, CAN_Rx_Data);		 // 处理接收到的PDO
		if (RxMeg.StdId == (0x600 + RS485_Addr)) // 如果是当前节点的SDO请求
			SDO_Process(&RxMeg, CAN_Rx_Data);	 // 处理SDO请求
	}
	if (HAL_OK == HAL_RetVal)
	{
		// 接收成功后的其他处理可以在这里添加
	}
}

void Process_TPDO(void) // 发送PDO
{	
	static int16_t angle_deg = 0;
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = 0x180 + RS485_Addr; // TPDO1的ID为0x180+节点ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	angle_deg = encoder_to_angle_degree(pos_actual);
	// printf("pos_actual=%d\n",pos_actual);
	// printf("angle_deg=%d\n",angle_deg);
	printf("iq_real=%d\n",Iq_real);
	// 将实际速度和位置信息打包到TPDO1数据中
	TPDO1_Data[0] = (real_speed_filter >> 24) & 0xff; // 实际速度高字节
	TPDO1_Data[1] = (real_speed_filter >> 16) & 0xff; // 实际速度次高字节
	TPDO1_Data[2] = (real_speed_filter >> 8) & 0xff;  // 实际速度次低字节
	TPDO1_Data[3] = (real_speed_filter) & 0xff;		  // 实际速度低字节

	TPDO1_Data[4] = (angle_deg >> 8) & 0xff; 
	TPDO1_Data[5] = (angle_deg) & 0xff; 
	
	TPDO1_Data[6] = (Iq_real >> 8) & 0xff;   // 实际交轴电流（转矩电流）高字节
	TPDO1_Data[7] = (Iq_real) & 0xff;        // 实际交轴电流（转矩电流）低字节

	
	
	if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, TPDO1_Data, &TxMailbox) != HAL_OK)
	{
		printf("TPDO1 send failed\n");
	}
}

void Process_TPDO2(void) // 发送电压温度电流信息
{
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = 0x280 + RS485_Addr; // TPDO2的ID为0x280+节点ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	// 将电压、温度、电流信息打包到TPDO2数据中
	TPDO2_Data[0] = (vbus_voltage >> 8) & 0xff;  // 总线电压高字节
	TPDO2_Data[1] = (vbus_voltage) & 0xff;       // 总线电压低字节
	
	TPDO2_Data[2] = (device_temperature >> 8) & 0xff;  // 设备温度高字节
	TPDO2_Data[3] = (device_temperature) & 0xff;       // 设备温度低字节
	
	TPDO2_Data[4] = (Iq_real >> 8) & 0xff;   // 实际交轴电流（转矩电流）高字节
	TPDO2_Data[5] = (Iq_real) & 0xff;        // 实际交轴电流（转矩电流）低字节
	
	TPDO2_Data[6] = (Id_real >> 8) & 0xff;   // 实际直轴电流（励磁电流）高字节
	TPDO2_Data[7] = (Id_real) & 0xff;        // 实际直轴电流（励磁电流）低字节

	if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, TPDO2_Data, &TxMailbox) != HAL_OK)
	{
		printf("TPDO2 send failed\n");
	}
}

void Process_RPDO(CAN_RxHeaderTypeDef *pHeader, uint8_t aData[]) // 处理接收PDO
{
	u16 rpdonum, rpdoid;
	rpdonum = pHeader->StdId & 0x780; // 获取功能码（高4位）
	rpdoid = pHeader->StdId & 0x07F;  // 获取节点ID（低7位）

	// 打印接收到的CAN帧信息              
	printf("RPDO Received - ID: 0x%03X, DLC: %d, Data: ", pHeader->StdId, pHeader->DLC);
	for (int i = 0; i < pHeader->DLC; i++)
	{
		printf("%02X ", aData[i]);
	}
	printf("| Type: 0x%03X, NodeID: %d\n", rpdonum, rpdoid);

	if (rpdoid == RS485_Addr) // 如果是发给当前节点的
	{
		switch (rpdonum) // 根据RPDO类型处理
		{
		case 0x200:
			target_Id = (aData[4] << 24) + (aData[5] << 16) + (aData[6] << 8) + aData[7];
			printf("current - Target: %d\n", target_Id);
			break;
		case 0x300:
			target_speed = (aData[4] << 24) + (aData[5] << 16) + (aData[6] << 8) + aData[7];
			printf("speed - Target: %d\n", target_speed);
			break;
		default:
			printf("Unknown RPDO type: 0x%03X\n", rpdonum);
			break;
		}
	}
	else
	{
		printf("RPDO not for this node (NodeID: %d, MyID: %d)\n", rpdoid, RS485_Addr);
	}
}

DICT_OBJECT SDO_Access_OBJ;									 // SDO访问对象
u8 SDO_Process(CAN_RxHeaderTypeDef *pHeader, uint8_t pbuf[]) // SDO处理函数
{
	u8 state = 0;						   // 处理状态
	uint32_t TxMailbox = 0;				   // 发送邮箱编号
										   // int i=0;
	TxMessage1.DLC = 8;					   // 数据长度为8字节
	TxMessage1.StdId = 0x580 + RS485_Addr; // SDO响应ID为0x580+节点ID
	TxMessage1.IDE = CAN_ID_STD;		   // 标准帧
	TxMessage1.RTR = CAN_RTR_DATA;		   // 数据帧

	HAL_GPIO_TogglePin(LED_2_GPIO_Port, LED_2_Pin); // 翻转LED2状态，指示SDO处理

	switch (pbuf[0]) // 根据SDO命令字处理
	{
	case SDO_W:			  // SDO写命令
	case SDO_W_1:		  // SDO写1字节
	case SDO_W_2:		  // SDO写2字节
	case SDO_W_4:		  // SDO写4字节
		if (Write_Access) // 如果允许写操作
		{
			SDO_Access_OBJ.Index = pbuf[1] + pbuf[2] * 0x100; // 获取索引
			SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
			SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针指向SDO数据区
			state = Write_OD(SDO_Access_OBJ);				  // 写对象字典

			memcpy(&CAN_Tx_Data[1], &pbuf[1], 7); // 复制索引等信息到响应数据中
			if (state == 1)						  // 写入成功
			{
				CAN_Tx_Data[0] = 0x60; // 成功响应代码
			}
			else // 写入失败
			{
				CAN_Tx_Data[0] = 0x80; // 失败响应代码
			}
			// 发送SDO响应
			if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) != HAL_OK)
			{
				printf("SDO response send failed\n");
			}
			break;
		}
		else // 如果不允许通用写操作，仅允许部分特定参数写入
		{
			SDO_Access_OBJ.Index = pbuf[1] + pbuf[2] * 0x100; // 获取索引
			SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
			SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针
			switch (SDO_Access_OBJ.Index)
			{
			case 0x8888: // 特殊参数允许写入
				Write_OD(SDO_Access_OBJ);
				break;
				/* 其他特殊参数写入
			case 0x3206:
				Write_OD(SDO_Access_OBJ);
				break;
			case 0x3101:
				Write_OD(SDO_Access_OBJ);
				break;
			case 0x3010:
				Write_OD(SDO_Access_OBJ);
				break;
				*/
			}
			// 串口响应相关代码（已注释）
			// memcpy(&USART3_TX_Buffer[2],&pbuf[1],7);
			// USART3_TX_Buffer[1]=0x60;
			// USART3_TX_Buffer[9]=Cal_sum(USART3_TX_Buffer,9);
			// DMA_Config_USART_TX3();
		}
		// Save_Parameter();  // 保存参数功能（已注释）
		break;
	case SDO_R:											  // SDO读命令
	case SDO_R_1:										  // SDO读1字节
	case SDO_R_2:										  // SDO读2字节
	case SDO_R_4:										  // SDO读4字节
		SDO_Access_OBJ.Index = pbuf[1] + pbuf[2] * 0x100; // 获取索引
		SDO_Access_OBJ.SubIndex = pbuf[3];				  // 获取子索引
		SDO_Access_OBJ.OD_pointer = &pbuf[4];			  // 数据指针
		state = Read_OD(SDO_Access_OBJ);				  // 读取对象字典

		memcpy(&CAN_Tx_Data[1], &pbuf[1], 7); // 复制索引等信息到响应数据中
		if (state == 1)						  // 读取成功
		{
			CAN_Tx_Data[0] = 0x60; // 成功响应代码
		}
		else // 读取失败
		{
			CAN_Tx_Data[0] = 0x80; // 失败响应代码
		}
		// 发送SDO响应
		if (HAL_CAN_AddTxMessage(&hcan, &TxMessage1, CAN_Tx_Data, &TxMailbox) != HAL_OK)
		{
			// 发送失败处理（如需要可在此添加代码）
			printf("SDO response send failed\n");
		}
		break;
	}
}
